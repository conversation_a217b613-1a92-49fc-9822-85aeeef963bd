{"digest": "A3714E20AA92939ACC1F90CB425CCCB5930654139AEA12FC6D1C822D5DDFB2E6", "schema": {"attributes": {"fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "primary_key": true, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "body"], "type": ["atom", "string"], "source": ["atom", "body"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": false, "index": true, "type": ["atom", "integer"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": "comments_approved_index", "nullable": true}, "name": ["atom", "approved"], "type": ["atom", "boolean"], "source": ["atom", "approved"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "primary_key": false, "foreign_key": true, "check_constraints": [], "index_name": "comments_user_id_index", "nullable": false}, "name": ["atom", "user_id"], "type": ["atom", "integer"], "source": ["atom", "user_id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "primary_key": false, "foreign_key": true, "check_constraints": [], "index_name": "comments_post_id_index", "nullable": false}, "name": ["atom", "post_id"], "type": ["atom", "integer"], "source": ["atom", "post_id"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "inserted_at"], "type": ["atom", "string"], "source": ["atom", "inserted_at"]}, "__struct__": "Field"}, {"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "string"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": false}, "name": ["atom", "updated_at"], "type": ["atom", "string"], "source": ["atom", "updated_at"]}, "__struct__": "Field"}], "source": ["atom", "comments"], "primary_key": {"attributes": {"meta": {"composite": false}, "fields": [{"attributes": {"meta": {"default": null, "index": false, "type": ["atom", "integer"], "primary_key": true, "foreign_key": false, "check_constraints": [], "index_name": null, "nullable": true}, "name": ["atom", "id"], "type": ["atom", "integer"], "source": ["atom", "id"]}, "__struct__": "Field"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}, "foreign_keys": [{"attributes": {"field": ["atom", "post_id"], "references_field": ["atom", "id"], "references_table": ["atom", "posts"]}, "__struct__": "ForeignKey"}, {"attributes": {"field": ["atom", "user_id"], "references_field": ["atom", "id"], "references_table": ["atom", "users"]}, "__struct__": "ForeignKey"}], "indices": [{"attributes": {"name": ["atom", "comments_approved_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": false, "index": true, "type": ["atom", "integer"], "primary_key": false, "foreign_key": false, "check_constraints": [], "index_name": "comments_approved_index", "nullable": true}, "name": ["atom", "approved"], "type": ["atom", "boolean"], "source": ["atom", "approved"]}, "__struct__": "Field"}], "composite": false, "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "comments_post_id_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "primary_key": false, "foreign_key": true, "check_constraints": [], "index_name": "comments_post_id_index", "nullable": false}, "name": ["atom", "post_id"], "type": ["atom", "integer"], "source": ["atom", "post_id"]}, "__struct__": "Field"}], "composite": false, "unique": false}, "__struct__": "Index"}, {"attributes": {"name": ["atom", "comments_user_id_index"], "type": ["atom", "btree"], "fields": [{"attributes": {"meta": {"default": null, "index": true, "type": ["atom", "integer"], "primary_key": false, "foreign_key": true, "check_constraints": [], "index_name": "comments_user_id_index", "nullable": false}, "name": ["atom", "user_id"], "type": ["atom", "integer"], "source": ["atom", "user_id"]}, "__struct__": "Field"}], "composite": false, "unique": false}, "__struct__": "Index"}]}, "__struct__": "<PERSON><PERSON><PERSON>"}}
defmodule Sample.Repo.Sqlite do
  use Ecto.Repo,
    otp_app: :sample_app,
    adapter: Ecto.Adapters.SQLite3
end

defmodule Sample.Repo.Postgres do
  use Ecto.Repo,
    otp_app: :sample_app,
    adapter: Ecto.Adapters.Postgres
end

defmodule Sample.Repo do
  @moduledoc """
  Dynamic repo module that delegates to the appropriate adapter-specific repo
  based on the ADAPTER environment variable.
  """

  @adapter_repo (case System.get_env("ADAPTER") do
                   "postgres" -> Sample.Repo.Postgres
                   _ -> Sample.Repo.Sqlite
                 end)

  defdelegate start_link(opts \\ []), to: @adapter_repo
  defdelegate stop(), to: @adapter_repo
  defdelegate stop(pid, timeout \\ 5000), to: @adapter_repo
  defdelegate child_spec(opts), to: @adapter_repo
  defdelegate __adapter__, to: @adapter_repo
  defdelegate config, to: @adapter_repo

  # Delegate all Ecto.Repo functions
  defdelegate all(queryable, opts \\ []), to: @adapter_repo
  defdelegate stream(queryable, opts \\ []), to: @adapter_repo
  defdelegate get(queryable, id, opts \\ []), to: @adapter_repo
  defdelegate get!(queryable, id, opts \\ []), to: @adapter_repo
  defdelegate get_by(queryable, clauses, opts \\ []), to: @adapter_repo
  defdelegate get_by!(queryable, clauses, opts \\ []), to: @adapter_repo
  defdelegate one(queryable, opts \\ []), to: @adapter_repo
  defdelegate one!(queryable, opts \\ []), to: @adapter_repo
  defdelegate aggregate(queryable, aggregate, field, opts \\ []), to: @adapter_repo
  defdelegate exists?(queryable, opts \\ []), to: @adapter_repo
  defdelegate insert(struct_or_changeset, opts \\ []), to: @adapter_repo
  defdelegate insert!(struct_or_changeset, opts \\ []), to: @adapter_repo
  defdelegate update(changeset, opts \\ []), to: @adapter_repo
  defdelegate update!(changeset, opts \\ []), to: @adapter_repo
  defdelegate insert_or_update(changeset, opts \\ []), to: @adapter_repo
  defdelegate insert_or_update!(changeset, opts \\ []), to: @adapter_repo
  defdelegate delete(struct_or_changeset, opts \\ []), to: @adapter_repo
  defdelegate delete!(struct_or_changeset, opts \\ []), to: @adapter_repo
  defdelegate insert_all(schema_or_source, entries, opts \\ []), to: @adapter_repo
  defdelegate update_all(queryable, updates, opts \\ []), to: @adapter_repo
  defdelegate delete_all(queryable, opts \\ []), to: @adapter_repo
  defdelegate preload(structs_or_struct_or_nil, preloads, opts \\ []), to: @adapter_repo
  defdelegate transaction(fun_or_multi, opts \\ []), to: @adapter_repo
  defdelegate rollback(value), to: @adapter_repo
  defdelegate checkout(fun, opts \\ []), to: @adapter_repo
  defdelegate in_transaction?, to: @adapter_repo
  defdelegate query(sql, params \\ [], opts \\ []), to: @adapter_repo
  defdelegate query!(sql, params \\ [], opts \\ []), to: @adapter_repo
  defdelegate prepare_query(operation, query, opts \\ []), to: @adapter_repo
  defdelegate execute(operation, query, params \\ [], opts \\ []), to: @adapter_repo
  defdelegate to_sql(operation, queryable), to: @adapter_repo
  defdelegate load(schema_or_types, data), to: @adapter_repo
  defdelegate dump(schema_or_types, data), to: @adapter_repo
end

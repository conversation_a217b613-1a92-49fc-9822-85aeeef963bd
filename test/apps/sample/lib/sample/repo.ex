defmodule Sample.Repo.Sqlite do
  use Ecto.Repo,
    otp_app: :sample_app,
    adapter: Ecto.Adapters.SQLite3
end

defmodule Sample.Repo.Postgres do
  use Ecto.Repo,
    otp_app: :sample_app,
    adapter: Ecto.Adapters.Postgres
end

# Simple alias based on ADAPTER env var
if System.get_env("ADAPTER") == "postgres" do
  defmodule Sample.Repo do
    use Ecto.Repo,
      otp_app: :sample_app,
      adapter: Ecto.Adapters.Postgres
  end
else
  defmodule Sample.Repo do
    use Ecto.Repo,
      otp_app: :sample_app,
      adapter: Ecto.Adapters.SQLite3
  end
end

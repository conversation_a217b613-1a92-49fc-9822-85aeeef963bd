import Config

env = config_env()
adapter = System.get_env("ADAPTER", "sqlite")

base_config = [
  stacktrace: true,
  show_sensitive_data_on_connection_error: true
]

# Override test-specific settings
test_config =
  if env == :test do
    [
      pool: Ecto.Adapters.SQL.Sandbox,
      pool_size: 1
    ]
  else
    [
      pool_size: 5
    ]
  end

# Configure based on adapter
adapter_config =
  case adapter do
    "postgres" ->
      [
        username: "postgres",
        password: "postgres",
        hostname: "postgres",
        database: "sample_app_#{env}"
      ]

    _ ->
      [
        database: Path.expand("../priv/#{env}_db.sqlite", __DIR__)
      ]
  end

config :sample_app, Sample.Repo, base_config ++ adapter_config ++ test_config

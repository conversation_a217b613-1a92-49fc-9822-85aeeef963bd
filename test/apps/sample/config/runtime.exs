import Config

env = config_env()
adapter = System.get_env("ADAPTER", "sqlite")

# Determine which repo to use
repo =
  case adapter do
    "postgres" -> Sample.Repo.Postgres
    _ -> Sample.Repo.Sqlite
  end

# Configure Ecto repositories
config :sample_app, ecto_repos: [repo]

# Configure drops_relation
config :sample_app, :drops,
  relation: [
    repo: repo,
    ecto_schema_namespace: [Sample, Schemas],
    view_module: &Sample.view_module/1
  ]

base_config = [
  stacktrace: true,
  show_sensitive_data_on_connection_error: true
]

# Override test-specific settings
test_config =
  if env == :test do
    [
      pool: Ecto.Adapters.SQL.Sandbox,
      pool_size: 1
    ]
  else
    [
      pool_size: 5
    ]
  end

# Configure SQLite repo
sqlite_config = [
  database: Path.expand("../priv/#{env}_db.sqlite", __DIR__),
  priv: "priv/repo"
]

config :sample_app, Sample.Repo.Sqlite, base_config ++ sqlite_config ++ test_config

# Configure PostgreSQL repo
postgres_config = [
  username: "postgres",
  password: "postgres",
  hostname: "postgres",
  database: "sample_app_#{env}",
  priv: "priv/repo"
]

config :sample_app, Sample.Repo.Postgres, base_config ++ postgres_config ++ test_config
